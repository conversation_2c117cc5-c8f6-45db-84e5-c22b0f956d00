<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <!-- Google Analytics -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-CK18X9ZGJ8"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-CK18X9ZGJ8');
    </script>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Preeklezen.nl - Live Transcriptie & Vertaling voor Kerken</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="192x192" href="/android-chrome-192x192.png">
    <link rel="icon" type="image/png" sizes="512x512" href="/android-chrome-512x512.png">
    <meta name="msapplication-TileColor" content="#2563eb">
    <meta name="theme-color" content="#2563eb">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&family=Montserrat:wght@700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary: #2563eb;
            --primary-dark: #1e40af;
            --primary-light: #3b82f6;
            --accent: #f0f4f8;
            --accent-dark: #dbe7f6;
            --secondary: #f1f5f9;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
            --bg: #ffffff;
            --surface: #fff;
            --text-dark: #1e293b;
            --text-main: #334155;
            --text-muted: #64748b;
            --text-light: #f8fafc;
            --border-radius: 16px;
            --border-radius-sm: 8px;
            --shadow-sm: 0 2px 5px rgba(0, 0, 0, 0.04), 0 1px 3px rgba(0, 0, 0, 0.03);
            --shadow-md: 0 6px 24px rgba(0, 0, 0, 0.05), 0 2px 6px rgba(0, 0, 0, 0.03);
            --shadow-lg: 0 10px 40px rgba(0, 0, 0, 0.08), 0 3px 14px rgba(0, 0, 0, 0.04);
            --transition: all 0.3s cubic-bezier(.4,0,.2,1);
            --container-max: 1280px;
            --font-main: 'Inter', 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            --font-secondary: 'Montserrat', Georgia, serif;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html {
            scroll-behavior: smooth;
            font-size: 16px;
            width: 100%;
            max-width: 100vw;
            overflow-x: hidden;
        }

        body {
            font-family: var(--font-main);
            color: var(--text-main);
            line-height: 1.7;
            overflow-x: hidden;
            background-color: var(--bg);
            text-rendering: optimizeLegibility;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            width: 100%;
            max-width: 100vw;
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        a {
            text-decoration: none;
            color: var(--primary);
            transition: var(--transition);
        }

        a:hover {
            color: var(--primary-dark);
            text-decoration: underline;
        }

        img {
            max-width: 100%;
            height: auto;
            display: block;
        }

        ul, ol {
            list-style-position: inside;
            padding-left: 0;
        }

        .container {
            width: 100%;
            max-width: var(--container-max);
            margin: 0 auto;
            padding: 0 1.5rem;
            box-sizing: border-box;
            position: relative;
            z-index: 1;
        }

        @media (max-width: 768px) {
            .container {
                padding: 0 1rem;
                width: 100%;
                max-width: 100%;
                box-sizing: border-box;
            }
        }

        @media (max-width: 576px) {
            .container {
                padding: 0 0.75rem;
                width: 100%;
                max-width: 100%;
                box-sizing: border-box;
            }
        }

        .text-center { text-align: center; }
        .visually-hidden {  position: absolute; width: 1px; height: 1px; padding: 0; margin: -1px; overflow: hidden; clip: rect(0, 0, 0, 0); white-space: nowrap; border: 0;  }

        h1, h2, h3, h4, h5, h6 {
            font-weight: 700;
            line-height: 1.3;
            margin-bottom: 1rem;
            color: var(--primary-dark);
            font-family: var(--font-main);
        }
        h1 { font-size: 2.8rem; margin-bottom: 1.5rem; }
        h2 { font-size: 2.2rem; margin-bottom: 1.25rem; }
        h3 { font-size: 1.75rem; margin-bottom: 1rem; }
        h4 { font-size: 1.4rem; margin-bottom: 0.75rem; }
        p { margin-bottom: 1.5rem; color: var(--text-main); }
        .text-light { color: var(--text-light); }
        .text-muted { color: var(--text-muted); }
        .lead { font-size: 1.2rem; font-weight: 400; line-height: 1.8; margin-bottom: 2rem; color: var(--text-muted); }
        .text-accent { color: var(--success); font-weight: 600; }
        .text-marker {  background: linear-gradient(to top, rgba(58, 90, 151, 0.2) 50%, transparent 50%); padding: 0.1em 0.3em; margin: -0.1em -0.3em; border-radius: 3px;  }
        .quote {  padding: 1.5rem 2rem 1.5rem 4rem; margin: 2rem 0; border-left: 5px solid var(--primary); background-color: var(--accent); font-style: italic; position: relative; border-radius: 0 var(--border-radius) var(--border-radius) 0;  }
        .quote::before {  content: '“'; position: absolute; left: 1rem; top: 0.5rem; font-size: 4rem; color: var(--primary-light); line-height: 1; font-family: var(--font-secondary);  }

        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            padding: 0.9rem 2rem;
            border-radius: var(--border-radius-sm);
            text-align: center;
            cursor: pointer;
            transition: var(--transition);
            border: none;
            font-size: 0.95rem;
            line-height: 1.5;
            letter-spacing: 0.5px;
            box-shadow: var(--shadow-sm);
            position: relative;
            overflow: hidden;
            z-index: 1;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
            text-decoration: none;
        }
        .btn::after {
            content: '';
            position: absolute;
            width: 0%;
            height: 100%;
            top: 0;
            left: 0;
            background-color: rgba(255, 255, 255, 0.1);
            transition: var(--transition);
            z-index: -1;
        }
        .btn:hover::after {
            width: 100%;
        }
        .btn-primary { background-color: var(--primary); color: var(--text-light); }
        .btn-primary:hover { background-color: var(--primary-dark); color: var(--text-light); }
        .btn-outline { background-color: transparent; border: 2px solid var(--primary); color: var(--primary); box-shadow: none; }
        .btn-outline:hover { background-color: var(--primary); color: var(--text-light); }
        .btn-lg { padding: 1.1rem 2.8rem; font-size: 1rem; }
        .btn-group { display: flex; gap: 1rem; flex-wrap: wrap; margin: 2rem 0; }

        .header {
            background-color: rgba(255, 255, 255, 0.97);
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
            box-shadow: none;
            position: fixed;
            width: 100%;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            transition: all 0.4s ease;
            border-bottom: 1px solid transparent;
            box-sizing: border-box;
        }
        .header.scrolled {
            box-shadow: var(--shadow-md);
            border-bottom: 1px solid rgba(230, 235, 245, 0.8);
            background-color: rgba(255, 255, 255, 0.98);
        }
        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.8rem 0;
            height: 75px;
            width: 100%;
            box-sizing: border-box;
        }
        @media (max-width: 768px) {
            .navbar {
                height: 70px;
                padding: 0.6rem 0;
                width: 100%;
            }
        }
        @media (max-width: 576px) {
            .navbar {
                height: 65px;
                padding: 0.5rem 0;
                width: 100%;
            }
        }
        .logo {
            display: flex;
            align-items: center;
            transition: var(--transition);
            flex-wrap: wrap;
        }
        @media (min-width: 1100px) {
            .logo a:first-child { margin-left: -60px; }
        }
        .logo:hover { transform: translateY(-2px); }
        .logo img { height: 38px; margin-right: 0.8rem; filter: drop-shadow(0 2px 4px rgba(37, 99, 235, 0.1)); }

        @media (max-width: 768px) {
            .logo {
                flex-wrap: nowrap;
            }
            .logo img {
                height: 34px;
                margin-right: 0.6rem;
            }
            .beheer-btn {
                padding: 0.5rem 0.9rem !important;
                margin-left: 0.6rem !important;
                font-size: 0.9rem !important;
            }
        }

        @media (max-width: 576px) {
            .logo {
                flex-wrap: nowrap;
            }
            .logo img {
                height: 30px;
                margin-right: 0.4rem;
            }
            .beheer-btn {
                padding: 0.4rem 0.6rem !important;
                margin-left: 0.4rem !important;
                min-width: auto !important;
            }
            .beheer-text { display: none; }
            .beheer-btn svg { margin-right: 0 !important; }
        }
        .logo-text { font-size: 1.4rem; font-weight: 700; color: var(--primary); font-family: var(--font-secondary); }
        .nav-links { display: flex; list-style: none; align-items: center; }
        .nav-links li { margin-left: 1.8rem; }
        .nav-links a {
            font-weight: 600;
            color: var(--text-main);
            position: relative;
            padding: 0.5rem 0.2rem;
            font-size: 0.95rem;
            transition: var(--transition);
        }
        .nav-links a::after {
            content: '';
            position: absolute;
            bottom: -3px;
            left: 0;
            width: 0%;
            height: 2px;
            background-color: var(--primary);
            transition: width 0.3s ease;
            border-radius: 5px;
        }
        .nav-links a:hover::after, .nav-links a.active::after { width: 100%; }
        .nav-links a:hover { color: var(--primary); text-decoration: none; }

        .mobile-menu-btn {
            display: none;
            flex-direction: column;
            justify-content: space-around;
            width: 30px;
            height: 22px;
            background: transparent;
            border: none;
            cursor: pointer;
            padding: 0;
            z-index: 1001;
            margin-right: 0.5rem;
            position: relative;
        }
        .mobile-menu-btn span {
            width: 100%;
            height: 3px;
            background-color: var(--primary);
            border-radius: 3px;
            transition: all 0.3s ease;
            transform-origin: center;
        }

        .mobile-menu-btn.active span:nth-child(1) { transform: translateY(9px) rotate(45deg); }
        .mobile-menu-btn.active span:nth-child(2) { opacity: 0; transform: scaleX(0); }
        .mobile-menu-btn.active span:nth-child(3) { transform: translateY(-10px) rotate(-45deg); }
        /* Style for the Preeklezen.nl banner below the header */
        /* Style for the Preeklezen.nl banner below the header (DEBUGGING) */
        /* Style for the Preeklezen.nl banner below the header */
        .brand-banner {
            text-align: left;
            padding: 0.7rem 2rem 0.7rem 2rem;
            background: linear-gradient(120deg, var(--primary-light, #3b82f6) 0%, var(--primary, #2563eb) 100%);
            color: var(--text-light, #f8fafc);
            font-family: var(--font-secondary, 'Montserrat', Georgia, serif);
            font-size: 1.3rem;
            font-weight: 700;
            line-height: 1.3;
            position: fixed;
            top: 75px;
            left: 0;
            right: 0;
            width: 100%;
            z-index: 999;
            box-sizing: border-box;
            transform: translateY(0);
            transition: transform 0.4s ease-in-out;
            box-shadow: var(--shadow-sm);
            letter-spacing: 0.5px;
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            gap: 0.8rem;
        }

        @media (max-width: 992px) {
            .brand-banner {
                padding: 0.7rem 1.5rem;
            }
            .beheer-btn { padding: 0.5rem 1rem !important; font-size: 0.9rem !important; }
        }

        @media (max-width: 768px) {
            .brand-banner {
                padding: 0.7rem 1rem;
                font-size: 1.1rem;
                flex-direction: column;
                align-items: flex-start;
                gap: 0.4rem;
                top: 70px;
                width: 100%;
                box-sizing: border-box;
            }
        }

        @media (max-width: 576px) {
            .brand-banner {
                padding: 0.7rem 0.75rem;
                font-size: 1rem;
                top: 65px;
                width: 100%;
                box-sizing: border-box;
            }
        }

        .brand-banner .brand-name {
            font-weight: 800;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            background: linear-gradient(90deg, #ffffff, #f0f4f8);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            letter-spacing: 0.5px;
            margin-right: 0.6rem;
        }

        .brand-banner .brand-tagline {
            font-size: 1rem;
            font-weight: 500;
            opacity: 0.95;
            letter-spacing: 0.2px;
            background: rgba(255,255,255,0.07);
            padding: 0.18em 0.95em;
            border-radius: 1em;
            color: #fff;
            box-shadow: 0 1px 4px rgba(30,64,175,0.08);
            display: inline-block;
        }

        @media (max-width: 768px) {
            .brand-banner .brand-tagline {
                font-size: 0.9rem;
                padding: 0.15em 0.8em;
                max-width: 100%;
                box-sizing: border-box;
            }
        }

        @media (max-width: 576px) {
            .brand-banner .brand-tagline {
                font-size: 0.8rem;
                padding: 0.12em 0.7em;
            }
        }

        .brand-banner.hidden {
            transform: translateY(-100%);
        }
        .hero {
    position: relative;
    padding-top: 110px; /* Minder ruimte tussen banner en website */
}
@media (max-width: 768px) {
    .hero {
        padding-top: 130px; /* Adjusted for stacked banner text */
    }
}
@media (max-width: 576px) {
    .hero {
        padding-top: 120px;
    }
}
@media (min-width: 1100px) {
  .hero::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 420px;
    height: 420px;
    background: url('https://www.transparenttextures.com/patterns/diamond-upholstery.png'), linear-gradient(135deg, #e0e7ff 60%, #fff 100%);
    background-size: 220px 220px, cover;
    border-radius: 0 0 0 160px;
    opacity: 0.26;
    z-index: 0;
    pointer-events: none;
  }
  .hero-content {
    z-index: 2;
    position: relative;
  }
  .hero-image {
    z-index: 2;
    position: relative;
  }
}
            padding-top: 105px;
            padding-bottom: 80px;
            min-height: auto;
            position: relative;
            display: flex;
            align-items: center;
            background: linear-gradient(135deg, #f8faff 0%, #e6f0ff 100%);
            overflow: hidden;
        }
        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%232563eb' fill-opacity='0.03'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
            opacity: 1;
            z-index: 0;
        }

        /* Church Background Images */
        .church-hero-bg::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url('https://images.unsplash.com/photo-1534798625261-00ffdb0abe77?auto=format&fit=crop&w=1600&q=80');
            background-size: cover;
            background-position: center;
            opacity: 0.12;
            z-index: 0;
            pointer-events: none;
        }

        .church-features-bg {
            position: relative;
        }

        .church-features-bg::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url('https://images.unsplash.com/photo-1601075361734-7e335ff3ec22?auto=format&fit=crop&w=1600&q=80');
            background-size: cover;
            background-position: center;
            opacity: 0.09;
            z-index: 0;
            pointer-events: none;
        }

        .church-how-it-works-bg {
            position: relative;
        }

        .church-how-it-works-bg::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url('https://images.unsplash.com/photo-1676485106418-7ff6bbf20107?auto=format&fit=crop&w=1600&q=80');
            background-size: cover;
            background-position: center;
            opacity: 0.11;
            z-index: 0;
            pointer-events: none;
        }

        .church-testimonials-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url('https://images.unsplash.com/photo-1518005068251-37900150dfca?auto=format&fit=crop&w=1600&q=80');
            background-size: cover;
            background-position: center;
            opacity: 0.15;
            z-index: 0;
            pointer-events: none;
        }

        .church-faq-bg {
            position: relative;
        }

        .church-faq-bg::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url('https://images.unsplash.com/photo-1438032005730-c779502df39b?auto=format&fit=crop&w=1600&q=80');
            background-size: cover;
            background-position: center;
            opacity: 0.1;
            z-index: 0;
            pointer-events: none;
        }
        .hero-content { position: relative; z-index: 1; padding: 2rem 0; }
        .hero-title {
            font-size: 3.2rem;
            font-weight: 800;
            line-height: 1.1;
            margin-bottom: 1.5rem;
            color: var(--primary-dark);
            font-family: var(--font-secondary);
        }
        .gradient-text {
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            color: transparent;
            position: relative;
            display: inline-block;
        }
        .gradient-text::after {
            content: '';
            position: absolute;
            left: 0;
            bottom: 5px;
            width: 100%;
            height: 8px;
            background: linear-gradient(135deg, var(--primary-light) 0%, var(--primary) 100%);
            opacity: 0.2;
            border-radius: 4px;
            z-index: -1;
        }
        .hero-subtitle {
            font-size: 1.3rem;
            margin-bottom: 3rem;
            max-width: 700px;
            color: var(--text-main);
            line-height: 1.6;
        }
        .hero-image {
            position: relative;
            z-index: 1;
            text-align: center;
            max-width: 600px;
            margin: 0 auto;
            transform: perspective(800px) rotateY(-5deg) rotateX(5deg) translateY(-10px);
            transition: var(--transition);
        }
        .hero-image:hover {
            transform: perspective(800px) rotateY(0deg) rotateX(0deg) translateY(-5px);
        }
        .hero-image img {
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-lg);
            transition: var(--transition);
        }
        .hero-grid { display: grid; grid-template-columns: repeat(2, 1fr); gap: 4rem; align-items: center; }
        .badge {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 2rem;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-right: 0.5rem;
            background-color: #ecfdf5;
            color: var(--success);
            border: 1px solid rgba(16, 185, 129, 0.2);
        }

        .section { padding: 6rem 0; }
        .section-header {
            text-align: center;
            margin-bottom: 5rem;
            max-width: 850px;
            margin-left: auto;
            margin-right: auto;
        }
        .section-header h2 {
            margin-bottom: 1.25rem;
            font-size: 2.5rem;
            font-weight: 800;
            position: relative;
            display: inline-block;
            font-family: var(--font-secondary);
        }
        .section-header h2::after {
    content: none;
}
        .section-subheading {
    color: var(--primary);
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1.5px;
    margin-bottom: 0.75rem;
    font-size: 0.9rem;
    position: relative;
    display: block;
    padding: 4px 12px;
    background-color: transparent;
    border-radius: 20px;
    text-align: center;
}
        .features-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2.5rem; }
        .feature-card {
            background-color: var(--text-light);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-sm);
            padding: 2.5rem 2rem;
            transition: var(--transition);
            display: flex;
            flex-direction: column;
            height: 100%;
            border: 1px solid rgba(203, 213, 225, 0.4);
            position: relative;
            overflow: hidden;
            z-index: 1;
        }
        .feature-card::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 5px;
            background: linear-gradient(90deg, var(--primary) 0%, var(--primary-light) 100%);
            transform: scaleX(0);
            transform-origin: left;
            transition: transform 0.4s ease;
            z-index: -1;
        }
        .feature-card:hover {
            transform: translateY(-8px);
            box-shadow: var(--shadow-md);
            border-color: rgba(203, 213, 225, 0.8);
        }
        .feature-card:hover::after {
            transform: scaleX(1);
        }
        .feature-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(37, 99, 235, 0.2) 100%);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1.5rem;
            color: var(--primary);
            transition: var(--transition);
            position: relative;
        }
        .feature-card:hover .feature-icon {
            transform: scale(1.1);
            color: var(--primary-dark);
        }
        .feature-icon svg {
            width: 28px;
            height: 28px;
            filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
        }
        .feature-title {
            margin-bottom: 0.75rem;
            font-size: 1.4rem;
            font-weight: 700;
            color: var(--text-dark);
            transition: var(--transition);
        }


        .bg-light { background-color: var(--secondary); }
        .steps { max-width: 800px; margin: 0 auto; }
        .step { display: flex; align-items: flex-start;  margin-bottom: 3.5rem; position: relative; }
        .step:last-child { margin-bottom: 0; }
        .step-number {
            flex-shrink: 0;
            width: 60px; height: 60px;
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            color: var(--text-light);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 1.6rem;
            margin-right: 2.5rem;
            position: relative;
            z-index: 2;
            box-shadow: 0 10px 20px rgba(37, 99, 235, 0.2);
            transition: var(--transition);
            border: 4px solid rgba(255, 255, 255, 0.8);
        }
        .step:hover .step-number {
            transform: scale(1.1) translateY(-5px);
            box-shadow: 0 15px 25px rgba(37, 99, 235, 0.25);
        }
        .step-content { flex-grow: 1; padding-top: 0.5rem;  }
        .step-title { margin-bottom: 0.5rem; font-size: 1.4rem; }
        .step p { margin-bottom: 0; color: var(--text-muted); }
        .step:not(:last-child)::after {
            content: ""; position: absolute;
            top: 60px;
            left: 27px;
            width: 3px;
            height: calc(100% + 0.5rem);
            background-color: var(--primary-light);
            z-index: 1;
            border-radius: 2px;
        }


        .pricing-cards { display: flex; justify-content: center; gap: 2.5rem; flex-wrap: wrap; }
        .pricing-card {
    background-color: #fff;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    padding: 3rem 2.5rem;
    flex: 1;
    min-width: 300px;
    max-width: 380px;
    transition: var(--transition);
    text-align: center;
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(203, 213, 225, 0.4);
    background-image: none;
}
        .pricing-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.03) 0%, rgba(37, 99, 235, 0.05) 100%);
            z-index: -1;
        }
        .pricing-card.featured {
            transform: scale(1.03);
            box-shadow: var(--shadow-lg);
            border: 2px solid var(--primary);
            background-color: rgba(255, 255, 255, 1);
        }
        .pricing-card.featured::before {
    content: none;
}
        .pricing-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: var(--shadow-lg);
            border-color: rgba(203, 213, 225, 0.8);
        }
        .pricing-card.featured:hover {
            transform: translateY(-10px) scale(1.05);
        }
        .pricing-header { margin-bottom: 2rem; }
        .pricing-name { font-size: 1.5rem;  margin-bottom: 0.75rem; font-weight: 600; }
        .pricing-price { font-size: 3rem;  font-weight: 800; color: var(--primary); margin-bottom: 0.25rem; }
        .pricing-duration { color: var(--text-muted); font-size: 0.9rem; margin-bottom: 1.5rem; }
        .pricing-features { margin-bottom: 2.5rem; list-style: none; text-align: left; padding-left: 1rem; }
        .pricing-features li { margin-bottom: 0.8rem; position: relative; padding-left: 1.8rem;  }
        .pricing-features li::before {
            content: "✓"; position: absolute; left: 0; top: 1px;
            color: var(--success); font-weight: 900; font-size: 1.1rem;
        }
        .installation-cost {
            text-align: center;
            margin-top: 3rem;
            padding: 2rem;
            background-color: rgba(59, 130, 246, 0.05);
            border-radius: var(--border-radius);
            border: 1px dashed rgba(59, 130, 246, 0.3);
            box-shadow: var(--shadow-sm);
            position: relative;
            overflow: hidden;
        }
        .installation-cost::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%233b82f608' fill-opacity='0.1' fill-rule='evenodd'%3E%3Cg fill='%233b82f608' fill-opacity='0.1' fill-rule='evenodd'%3E%3Ccircle cx='3' cy='3' r='3'/%3E%3Ccircle cx='13' cy='13' r='3'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
            z-index: -1;
        }
        .installation-cost p { margin-bottom: 0.5rem; font-size: 1.1rem; }
        .installation-cost strong { color: var(--primary-dark); font-size: 1.3rem; }


        .testimonials { position: relative; overflow: hidden; }
        .testimonials-bg {  position: absolute; top: 0; left: 0; width: 100%; height: 100%; opacity: 0.05; background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100'%3E%3Cpath d='M12.5 12.5h75v75h-75z' fill='%233a5a97' fill-opacity='.1'/%3E%3C/svg%3E"); z-index: -1;  }
        .testimonials-slider {
            display: grid;
            grid-auto-flow: column;
            grid-auto-columns: calc(33.333% - 1.7rem);
            gap: 2.5rem;
            overflow-x: auto;
            scroll-snap-type: x mandatory;
            padding: 2rem 0.5rem;
            scrollbar-width: thin;
            scrollbar-color: var(--primary-light) var(--accent);
        }
        .testimonials-slider::-webkit-scrollbar { height: 8px; }
        .testimonials-slider::-webkit-scrollbar-track { background: var(--accent); border-radius: 4px; }
        .testimonials-slider::-webkit-scrollbar-thumb { background-color: var(--primary-light); border-radius: 4px; border: 2px solid var(--accent); }
        .testimonial-card {
            scroll-snap-align: start;
            background-color: var(--text-light);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-sm);
            padding: 2.5rem 2rem 2rem;
            transition: var(--transition);
            position: relative;
            min-width: 300px;
            border: 1px solid rgba(203, 213, 225, 0.4);
            display: flex;
            flex-direction: column;
            background-image: url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M20 34.5c-.7 0-1.4-.2-2-.5L5.5 26c-1.3-.7-2-2-2-3.5V13c0-1.5.7-2.8 2-3.5L18 1.5c1.3-.7 2.8-.7 4 0L34.5 9.5c1.3.7 2 2 2 3.5v9.5c0 1.5-.7 2.8-2 3.5L22 34c-.6.3-1.3.5-2 .5z' stroke='%233b82f624' fill='none'/%3E%3C/svg%3E");
            background-position: 100% 0;
            background-repeat: no-repeat;
        }
        .testimonial-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-md);
            border-color: rgba(203, 213, 225, 0.8);
        }
        .testimonial-content {
            margin-bottom: 1.5rem;
            font-style: italic;
            color: var(--text-main);
            flex-grow: 1;
            position: relative;
            padding-top: 1rem;
        }
        .testimonial-content::before {
            content: '"';
            font-size: 4rem;
            color: var(--primary-light);
            opacity: 0.3;
            position: absolute;
            top: -0.5rem;
            left: -0.5rem;
            line-height: 1;
            font-family: Georgia, serif;
        }
        .testimonial-author { display: flex; align-items: center; margin-top: auto;  }
        .testimonial-avatar {
             width: 50px; height: 50px; border-radius: 50%; margin-right: 1rem;
             background: linear-gradient(135deg, var(--primary-light), var(--accent));
             display: flex; align-items: center; justify-content: center;
             color: var(--primary-dark); font-weight: 700; font-size: 1.2rem;
             border: 2px solid var(--text-light);
        }
        .testimonial-info { flex-grow: 1; }
        .testimonial-name { font-weight: 700; margin-bottom: 0.1rem; }
        .testimonial-role { color: var(--text-muted); font-size: 0.85rem; }
        .testimonial-rating { color: #FFD700; margin-top: 0.5rem; font-size: 1.1rem; letter-spacing: 1px; }


        .faq-list { max-width: 850px; margin: 0 auto; }
        .faq-item {
            margin-bottom: 1.25rem;
            border-radius: var(--border-radius);
            background-color: var(--text-light);
            box-shadow: var(--shadow-sm);
            overflow: hidden;
            border: 1px solid rgba(203, 213, 225, 0.4);
            transition: all 0.3s ease;
        }
        .faq-item.active {
            margin-bottom: 1.75rem;
            box-shadow: var(--shadow-md);
            border-color: rgba(37, 99, 235, 0.2);
        }
        .faq-question {
    display: flex;
    align-items: center;
            padding: 1.5rem 1.75rem;
            cursor: pointer;
            font-weight: 600;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all 0.3s ease;
            user-select: none;
            color: var(--text-dark);
        }
        .faq-question:hover {
            background-color: var(--accent);
            color: var(--primary-dark);
        }
        .faq-question span {
            flex-grow: 1;
            margin-right: 1rem;
        }
        .faq-icon {
    font-size: 1.6rem;
    color: var(--primary);
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: none;
    border-radius: 0;
    margin-right: 12px;
    transition: color 0.3s;
}
        .faq-item.active .faq-icon {
    transform: rotate(45deg);
    color: var(--primary);
    background: none;
}
        .faq-answer {
            padding: 0 1.5rem;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.4s ease-out, padding 0.4s ease-out;
            color: var(--text-muted);
            font-size: 0.95rem;
        }
        .faq-item.active .faq-answer {
            max-height: 500px;
            padding: 0 1.5rem 1.5rem;
            transition: max-height 0.5s ease-in, padding 0.5s ease-in;
        }
        .faq-answer p:last-child { margin-bottom: 0; }


        .cta {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            color: var(--text-light);
            padding: 6rem 0;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        .cta::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.08'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
            opacity: 0.2;
            z-index: 0;
        }
        .cta-title {
            color: var(--text-light);
            font-size: 2.8rem;
            margin-bottom: 1.25rem;
            font-weight: 800;
            position: relative;
            z-index: 1;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .cta-text {
            max-width: 700px;
            margin: 0 auto 3rem;
            font-size: 1.2rem;
            opacity: 0.9;
            position: relative;
            z-index: 1;
            line-height: 1.6;
        }
        .btn-cta {
            background-color: var(--text-light);
            color: var(--primary-dark);
            font-weight: 700;
            position: relative;
            z-index: 1;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }
        .btn-cta:hover {
            background-color: rgba(255, 255, 255, 0.95);
            color: var(--primary-dark);
            transform: translateY(-3px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
        }


        .footer { background-color: #25344f;  color: rgba(255, 255, 255, 0.7); padding: 5rem 0 2rem; font-size: 0.9rem; }
        .footer-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(220px, 1fr)); gap: 3rem; margin-bottom: 3rem; }
        .footer-logo { margin-bottom: 1.5rem; display: inline-block; }
        .footer-logo img { height: 35px; filter: brightness(0) invert(1) opacity(0.8);  }
        .footer-company p { margin-bottom: 1.5rem; }
        .footer-social { display: flex; gap: 0.8rem; margin-top: 1.5rem; }
        .social-icon {
             width: 38px; height: 38px; border-radius: 50%;
             background-color: rgba(255, 255, 255, 0.1);
             color: rgba(255, 255, 255, 0.7);
             display: flex; align-items: center; justify-content: center;
             transition: var(--transition);
             font-size: 1.1rem;
        }
        .social-icon:hover { background-color: var(--primary); color: var(--text-light); transform: translateY(-3px); }
        .footer-heading { font-size: 1.1rem; color: var(--text-light); margin-bottom: 1.5rem; font-weight: 600; }
        .footer-links { list-style: none; }
        .footer-links li { margin-bottom: 0.8rem; }
        .footer-links a { color: rgba(255, 255, 255, 0.7); transition: var(--transition); }
        .footer-links a:hover { color: var(--text-light); padding-left: 8px; text-decoration: none; }
        .footer-contact-item { display: flex; align-items: flex-start; margin-bottom: 1rem; }
        .contact-icon { margin-right: 0.8rem; color: var(--primary-light); width: 20px; text-align: center; margin-top: 2px; }
        .footer-bottom { padding-top: 2rem; text-align: center; border-top: 1px solid rgba(255, 255, 255, 0.1); font-size: 0.85rem; }
        .footer-bottom a { color: var(--primary-light); font-weight: 600; }
        .footer-bottom a:hover { color: var(--text-light); }
        .creator-credit {
            display: block;
            margin-top: 2rem;
            font-size: 1.5rem;
            font-weight: 700;
            color: rgba(255, 255, 255, 0.8);
            text-align: center;
            letter-spacing: 1px;
            font-family: var(--font-secondary);
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            transition: var(--transition);
        }
        .creator-credit:hover {
            color: var(--text-light);
            transform: translateY(-2px);
            text-decoration: none;
        }
        @media (max-width: 768px) {
            .creator-credit {
                font-size: 1.3rem;
                margin-top: 1.5rem;
            }
        }
        @media (max-width: 576px) {
            .creator-credit {
                font-size: 1.2rem;
                margin-top: 1.2rem;
            }
        }


        .form-group { margin-bottom: 1.5rem; }
        .form-label { display: block; margin-bottom: 0.5rem; font-weight: 600; font-size: 0.9rem; color: var(--text-muted); }
        .form-control {
            width: 100%; padding: 0.9rem 1rem; font-size: 1rem;
            border: 1px solid #ced4da; border-radius: var(--border-radius);
            transition: var(--transition); background-color: #fff; color: var(--text-dark);
        }
        .form-control:focus { outline: none; border-color: var(--primary-light); box-shadow: 0 0 0 3px rgba(58, 90, 151, 0.15); }
        textarea.form-control { min-height: 120px; resize: vertical; }


        @keyframes fadeIn { from { opacity: 0; transform: translateY(20px); } to { opacity: 1; transform: translateY(0); } }
        @keyframes countUp { from { opacity: 0; transform: translateY(10px); } to { opacity: 1; transform: translateY(0); } }

        .animate-on-scroll {
            opacity: 0;
            transition: opacity 0.6s ease-out, transform 0.6s ease-out;
        }
        .animate-on-scroll.fade-in { transform: translateY(30px); }
        .animate-on-scroll.is-visible { opacity: 1; transform: translateY(0); }


        .delay-100 { transition-delay: 0.1s; }
        .delay-200 { transition-delay: 0.2s; }
        .delay-300 { transition-delay: 0.3s; }
        .delay-400 { transition-delay: 0.4s; }
        .delay-500 { transition-delay: 0.5s; }


        .mt-1 { margin-top: 0.5rem; } .mt-2 { margin-top: 1rem; } .mt-3 { margin-top: 1.5rem; } .mt-4 { margin-top: 2rem; } .mt-5 { margin-top: 3rem; }
        .mb-1 { margin-bottom: 0.5rem; } .mb-2 { margin-bottom: 1rem; } .mb-3 { margin-bottom: 1.5rem; } .mb-4 { margin-bottom: 2rem; } .mb-5 { margin-bottom: 3rem; }
        .py-1 { padding-top: 0.5rem; padding-bottom: 0.5rem; } .py-2 { padding-top: 1rem; padding-bottom: 1rem; } .py-3 { padding-top: 1.5rem; padding-bottom: 1.5rem; } .py-4 { padding-top: 2rem; padding-bottom: 2rem; } .py-5 { padding-top: 3rem; padding-bottom: 3rem; } .py-6 { padding-top: 5rem; padding-bottom: 5rem; }
        .px-1 { padding-left: 0.5rem; padding-right: 0.5rem; } .px-2 { padding-left: 1rem; padding-right: 1rem; } .px-3 { padding-left: 1.5rem; padding-right: 1.5rem; } .px-4 { padding-left: 2rem; padding-right: 2rem; } .px-5 { padding-left: 3rem; padding-right: 3rem; }
        .text-right { text-align: right; } .text-left { text-align: left; }


        @media (max-width: 992px) {
            html { font-size: 15px; }
            .hero-title { font-size: 2.6rem; }
            .hero-subtitle { font-size: 1.2rem; }
            .hero-grid { grid-template-columns: 1fr; gap: 2rem; text-align: center; }
            .hero-image { margin: 2rem auto 0; max-width: 450px; }
            .features-grid { grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); }
            .testimonials-slider { grid-auto-columns: calc(50% - 1.25rem);  }
            .step::after { left: 27px; }
        }

        @media (max-width: 768px) {
            .section { padding: 4rem 0; }
            .mobile-menu-btn {
                display: flex;
                position: relative;
                right: 0;
                z-index: 1002;
            }
            .nav-links {
                position: absolute;
                top: 70px;
                left: 0;
                width: 100%;
                background-color: var(--text-light);
                flex-direction: column;
                align-items: center;
                padding: 1.5rem 0;
                box-shadow: var(--shadow-md);
                transform: translateY(-120%);
                transition: transform 0.35s ease-in-out;
                border-top: 1px solid var(--accent);
                height: calc(100vh - 70px);
                overflow-y: auto;
                box-sizing: border-box;
                z-index: 1000;
            }
            .nav-links.active { transform: translateY(0); }
            .nav-links li { margin: 1rem 0;  margin-left: 0; width: 100%; text-align: center; box-sizing: border-box; }
            .nav-links a { font-size: 1.1rem; padding: 0.8rem 1rem; display: block; width: 100%; box-sizing: border-box; }
            .nav-links a::after { bottom: -5px; height: 3px; }
            .language-select { margin-left: 0; margin-top: 1rem; }

            .testimonials-slider { grid-auto-columns: calc(85% - 1rem);  }
            .pricing-card.featured { transform: scale(1); }
            .pricing-cards { gap: 1.5rem; }
            .footer-grid { grid-template-columns: 1fr; text-align: center; }
            .footer-social { justify-content: center; }
            .footer-contact-item { justify-content: center; }
            .contact-icon { margin-right: 0.5rem; }
        }

        @media (max-width: 576px) {
            html { font-size: 14px; }
            .hero-title { font-size: 2.1rem; }
            .hero-subtitle { font-size: 1.1rem; }
            .btn-group { flex-direction: column; align-items: center; }
            .btn-group .btn { width: 100%; max-width: 300px; margin-bottom: 0.8rem; }
            .btn-group .btn:last-child { margin-bottom: 0; }
            .step { flex-direction: column; align-items: center; text-align: center; }
            .step-number { margin-right: 0; margin-bottom: 1rem; }
            .step::after { display: none;  }
            .pricing-card { max-width: none; }
            .testimonials-slider { grid-auto-columns: 90%; }
            .demo-tab { padding: 0.8rem 1rem; font-size: 0.9rem; }
        }


        .language-select { position: relative; display: inline-flex; align-items: center; cursor: pointer; margin-left: 1rem; padding: 0.5rem; border-radius: var(--border-radius); transition: background-color 0.3s ease; }
        .language-select:hover { background-color: var(--accent); }
        .language-icon { margin-right: 0.3rem; font-size: 1.1rem; }
        .language-current { font-weight: 600; color: var(--primary); font-size: 0.9rem; }
        .language-dropdown { display: none; position: absolute; top: 100%; right: 0; background-color: var(--text-light); box-shadow: var(--shadow-md); border-radius: var(--border-radius); min-width: 130px; padding: 0.5rem 0; z-index: 1010; border: 1px solid var(--accent); margin-top: 5px; }
        .language-select:hover .language-dropdown { display: block; }
        .language-option { padding: 0.6rem 1rem; transition: var(--transition); font-size: 0.9rem; color: var(--text-dark); display: block; white-space: nowrap; }
        .language-option:hover { background-color: var(--accent); color: var(--primary); }


        .counter-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 2rem;
            text-align: center;
            margin: 5rem 0 3rem;
        }
        .counter-item {
            padding: 1.5rem;
            position: relative;
            background-color: var(--text-light);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-sm);
            transition: var(--transition);
        }
        .counter-item:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-md);
        }
        .counter {
            font-size: 3.2rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            color: transparent;
            margin-bottom: 0.5rem;
            display: block;
        }
        .counter-title {
            font-size: 1rem;
            color: var(--text-dark);
            font-weight: 600;
        }


        .demo-tabs {
            display: flex;
            border-bottom: 2px solid rgba(203, 213, 225, 0.4);
            margin-bottom: 3rem;
            justify-content: center;
            flex-wrap: wrap;
            gap: 1rem;
            padding-bottom: 0.5rem;
        }
        .demo-tab {
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            border-bottom: 3px solid transparent;
            margin-bottom: -2px;
            color: var(--text-muted);
            text-align: center;
            border-radius: var(--border-radius-sm);
            background-color: rgba(255, 255, 255, 0.7);
            box-shadow: var(--shadow-sm);
        }
        .demo-tab:hover {
            color: var(--primary);
            background-color: rgba(255, 255, 255, 0.95);
            transform: translateY(-3px);
        }
        .demo-tab.active {
            color: var(--text-light);
            background-color: var(--primary);
            border-bottom-color: var(--primary-dark);
            box-shadow: var(--shadow-md);
        }
        .demo-content { display: none; }
        .demo-content.active { display: block; animation: fadeIn 0.6s ease-out forwards; }
        .demo-image img { border-radius: var(--border-radius); box-shadow: var(--shadow-md); margin: 0 auto;  }


        ::-webkit-scrollbar { width: 10px; height: 10px; }
        ::-webkit-scrollbar-track { background: #f1f1f1; border-radius: 5px; }
        ::-webkit-scrollbar-thumb { background: var(--primary-light); border-radius: 5px; }
        ::-webkit-scrollbar-thumb:hover { background: var(--primary); }
        body { scrollbar-width: thin; scrollbar-color: var(--primary-light) #f1f1f1; }


        ::selection { background-color: var(--primary-light); color: var(--primary-dark); }


        .feature-icon svg { fill: currentColor; }
        .contact-icon svg { fill: currentColor; }
        .social-icon svg { fill: currentColor; width: 18px; height: 18px; }

    .frontpage-immersive {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;
}
.frontpage-img {
    width: 100%;
    max-width: 700px;
    height: 60vh;
    min-height: 340px;
    object-fit: cover;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    margin-left: auto;
    margin-right: 0;
}
@media (max-width: 1100px) {
    .frontpage-img {
        max-width: 100%;
        height: 38vh;
        min-height: 180px;
    }
}
@media (max-width: 768px) {
    .frontpage-immersive {
        justify-content: center;
    }
    .frontpage-img {
        width: 100%;
        height: 28vh;
        min-height: 120px;
    }
}
</style>
</head>
<body>

    <header class="header" id="header">
        <div class="container">
            <nav class="navbar">
                <div class="logo">
                    <a href="https://wesotronic.nl/audiovisueel/" target="_blank">
                        <img src="wesotronic.png" alt="Wesotronic logo" style="height:38px; margin-right:0.8rem;">
                    </a>
                    <a href="https://preektekst.wesotronic.nl" target="_blank" class="btn beheer-btn" style="margin-left:1rem; background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%); color: var(--text-light); font-size:0.95rem; font-weight:600; border-radius:8px; padding:0.6rem 1.2rem; display:inline-flex; align-items:center; transition:all 0.3s ease; box-shadow: 0 4px 12px rgba(37, 99, 235, 0.2);"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16" style="margin-right:6px;"><path d="M9.405 1.05c-.413-1.4-2.397-1.4-2.81 0l-.1.34a1.464 1.464 0 0 1-2.105.872l-.31-.17c-1.283-.698-2.686.705-1.987 1.987l.169.311c.446.82.023 1.841-.872 2.105l-.34.1c-1.4.413-1.4 2.397 0 2.81l.34.1a1.464 1.464 0 0 1 .872 2.105l-.17.31c-.698 1.283.705 2.686 1.987 1.987l.311-.169a1.464 1.464 0 0 1 2.105.872l.1.34c.413 1.4 2.397 1.4 2.81 0l.1-.34a1.464 1.464 0 0 1 2.105-.872l.31.17c1.283.698 2.686-.705 1.987-1.987l-.169-.311a1.464 1.464 0 0 1 .872-2.105l.34-.1c1.4-.413 1.4-2.397 0-2.81l-.34-.1a1.464 1.464 0 0 1-.872-2.105l.17-.31c.698-1.283-.705-2.686-1.987-1.987l-.311.169a1.464 1.464 0 0 1-2.105-.872l-.1-.34zM8 10.93a2.929 2.929 0 1 1 0-5.86 2.929 2.929 0 0 1 0 5.858z"/></svg><span class="beheer-text">Beheeromgeving</span></a>
                </div>
                <button class="mobile-menu-btn" id="mobile-menu-btn" aria-label="Menu openen/sluiten" aria-expanded="false">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
                <ul class="nav-links" id="nav-links">
                    <li><a href="#home">Home</a></li>
                    <li><a href="#features">Functies</a></li>
                    <li><a href="#how-it-works">Hoe het werkt</a></li>
                    <li><a href="#pricing">Prijzen</a></li>
                    <li><a href="#testimonials">Ervaringen</a></li>
                    <li><a href="#faq">FAQ</a></li>
                    <li><a href="#contact" class="btn" style="background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%); color: white; font-size:0.95rem; font-weight:600; border-radius:8px; padding:0.6rem 1.2rem; display:inline-flex; align-items:center; transition:all 0.3s ease; box-shadow: 0 4px 12px rgba(37, 99, 235, 0.2);"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16" style="margin-right:6px;"><path d="M0 4a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V4Zm2-1a1 1 0 0 0-1 1v.217l7 4.2 7-4.2V4a1 1 0 0 0-1-1H2Zm13 2.383-4.708 2.825L15 11.105V5.383Zm-.034 6.876-5.64-3.471L8 9.583l-1.326-.795-5.64 3.47A1 1 0 0 0 2 13h12a1 1 0 0 0 .966-.741ZM1 11.105l4.708-2.897L1 5.383v5.722Z"/></svg>Contact</a></li>
                    <li>
                        <div class="language-select">
                            <span class="language-icon" aria-hidden="true">
                                <svg xmlns="http://www.w3.org/2000/svg" height="1em" viewBox="0 0 512 512"><path d="M352 256c0 22.2-1.2 43.6-3.3 64H163.3c-2.1-20.4-3.3-41.8-3.3-64s1.2-43.6 3.3-64H348.7c2.1 20.4 3.3 41.8 3.3 64zm28.8-64H503.9c5.3 20.5 8.1 41.9 8.1 64s-2.8 43.5-8.1 64H380.8c2.1-20.6 3.2-42 3.2-64s-1.1-43.4-3.2-64zm112.6-32H376.7c-10-63.9-29.8-117.4-55.3-151.6c78.3 20.7 142 77.8 171.9 151.6zm-149.1 0H167.7c6.1-36.4 15.5-68.6 27-94.7c10.5-23.6 22.2-40.7 33.5-51.5C239.4 3.2 248.7 0 256 0s16.6 3.2 27.8 13.8c11.3 10.8 23 27.9 33.5 51.5c11.6 26 21 58.2 27 94.7zm-209 0H18.5C48.4 88.7 112.1 31.6 190.4 11.8C164.9 46.2 145.1 99.7 135.1 163.8zM0 224c0-22.1 2.8-43.5 8.1-64h119.8c-2.1 20.6-3.2 42-3.2 64s1.1 43.4 3.2 64H8.1C2.8 267.5 0 246.1 0 224zm128 160H8.1c5.3 20.5 8.1 41.9 8.1 64s-2.8 43.5-8.1 64h119.8c-2.1-20.6-3.2-42-3.2-64s1.1-43.4 3.2-64zm384 0c2.1 20.6 3.2 42 3.2 64s-1.1 43.4-3.2 64H503.9c5.3-20.5 8.1-41.9 8.1-64s-2.8-43.5-8.1-64H380.8zM190.4 499.9c78.3-19.8 142-76.9 171.9-151.7H135.1C145.1 412 164.9 465.5 190.4 499.9zM256 512c8.4 0 17.4-3.4 28.7-14.5c11.6-11.4 23.7-29.1 35.4-53.9c11.2-23.8 20-54.3 25.9-87.8H165.9c5.9 33.5 14.7 64 25.9 87.8c11.7 24.8 23.8 42.5 35.4 53.9C238.6 508.6 247.6 512 256 512z"/></svg>
                            </span>
                            <span class="language-current">NL</span>
                        </div>
                    </li>
                </ul>
            </nav>
        </div>
    </header>
<div class="brand-banner">
    <span class="brand-name">Preeklezen.nl</span>
    <span class="brand-tagline">Toegankelijkheid voor kerken – een product van Wesotronic AV</span>
</div>

    <section id="home" class="hero church-hero-bg">
        <div class="container">
            <div class="hero-grid">
                <div class="hero-content animate-on-scroll fade-in">
                    <h1 class="hero-title">Maak preken <span class="gradient-text">toegankelijk</span> voor iedereen</h1>
                    <p class="hero-subtitle">Preeklezen transcribeert en ondertitelt uw kerkdiensten live. Inclusief automatische AI-vertaling naar 200+ talen.</p>
                    <div class="btn-group">
                        <a href="#pricing" class="btn btn-primary btn-lg">Bekijk Prijzen</a>
                        <a href="#demo" class="btn btn-outline btn-lg">Bekijk Demo</a>
                    </div>
                    <p class="mt-4" style="font-size: 1.1rem; font-weight: 500; color: #2563eb;">
                        <span class="badge badge-success me-3" style="font-size:0.95rem; padding:0.28em 0.95em; border-radius:2em;">NIEUW!</span>
                        <span class="gradient-text"><strong>Downloadbaar tekstarchief</span> waarin preken automatisch kunnen worden omgezet van spreektaal naar schrijftaal, bruikbaar als naslagwerk, als concept voor leespreken of een prekenbundel.</strong>
                    </p>
                </div>
                <div class="hero-image animate-on-scroll fade-in delay-200 frontpage-immersive">
    <img src="frontpage.jpg" alt="Frontpage immersive kerkbeeld" class="frontpage-img">
</div>
            </div>
        </div>
    </section>


    <section id="features" class="section church-features-bg">
        <div class="container">
            <div class="section-header animate-on-scroll fade-in">
                <p class="section-subheading">Wat wij bieden</p>
                <h2>Kernfuncties van Preeklezen</h2>
                <p class="lead">Ontworpen om kerkdiensten toegankelijker te maken met gebruiksvriendelijke technologie.</p>
            </div>

            <div class="features-grid">
                <div class="feature-card animate-on-scroll fade-in">
                    <div class="feature-icon"><svg xmlns="http://www.w3.org/2000/svg" height="1em" viewBox="0 0 512 512"><path d="M362.7 19.3L314.3 67.7 444.3 197.7l48.4-48.4c25-25 25-65.5 0-90.5L453.3 19.3c-25-25-65.5-25-90.5 0zm-71 71L58.6 323.5c-10.4 10.4-18 23.3-22.2 37.4L1 481.2C-1.5 489.7 .8 498.8 7 505s15.3 8.5 23.7 6.1l120.3-35.4c14.1-4.2 27-11.8 37.4-22.2L421.7 220.3 291.7 90.3z"/></svg></div>
                    <h3 class="feature-title">Live Transcriptie</h3>
                    <p>Real-time omzetting van gesproken woord naar tekst. Ideaal voor doven, slechthorenden en meelezers.</p>
                </div>

                <div class="feature-card animate-on-scroll fade-in delay-100">
                    <div class="feature-icon"><svg xmlns="http://www.w3.org/2000/svg" height="1em" viewBox="0 0 576 512"><path d="M544 0H32C14.3 0 0 14.3 0 32v448c0 17.7 14.3 32 32 32h512c17.7 0 32-14.3 32-32V32c0-17.7-14.3-32-32-32zM96 160H16C7.2 160 0 167.2 0 176v16c0 8.8 7.2 16 16 16h66.9c-3.5 14.4-5.6 29.5-5.6 45s2 30.6 5.6 45H16c-8.8 0-16 7.2-16 16v16c0 8.8 7.2 16 16 16h80c48.6 0 92.3-25.7 116.3-64H304c8.8 0 16-7.2 16-16v-16c0-8.8-7.2-16-16-16H212.3c-12.4-19.5-28.2-35.4-46.6-46.6C230.3 242.1 288 189.4 288 128c0-70.7-57.3-128-128-128H16C7.2 0 0 7.2 0 16v16c0 8.8 7.2 16 16 16h144c44.1 0 80 35.9 80 80s-35.9 80-80 80z"/></svg></div>
                    <h3 class="feature-title">Live Vertaling</h3>
                    <p>Vertaal de preek direct naar 200+ talen. Maak de dienst toegankelijk voor een internationaal publiek.</p>
                </div>

                <div class="feature-card animate-on-scroll fade-in delay-200">
                     <div class="feature-icon"><svg xmlns="http://www.w3.org/2000/svg" height="1em" viewBox="0 0 512 512"><path d="M0 96C0 60.7 28.7 32 64 32H448c35.3 0 64 28.7 64 64V416c0 35.3-28.7 64-64 64H64c-35.3 0-64-28.7-64-64V96zm64 64V416H224V160H64zm384 0H288V416H448V160z"/></svg></div>
                    <h3 class="feature-title">Web App voor Elk Apparaat</h3>
                    <p>Toegankelijk via elke browser op smartphones, tablets en laptops. Geen app installatie nodig.</p>
                </div>

                <div class="feature-card animate-on-scroll fade-in delay-300">
                    <div class="feature-icon"><svg xmlns="http://www.w3.org/2000/svg" height="1em" viewBox="0 0 512 512"><path d="M0 96C0 60.7 28.7 32 64 32H196.8c19.1 0 37.4 7.6 50.9 21.1L289.9 96H448c35.3 0 64 28.7 64 64V416c0 35.3-28.7 64-64 64H64c-35.3 0-64-28.7-64-64V96zM64 80c-8.8 0-16 7.2-16 16V416c0 8.8 7.2 16 16 16H448c8.8 0 16-7.2 16-16V160c0-8.8-7.2-16-16-16H286.6c-10.6 0-20.8-4.2-28.3-11.7L213.1 87c-4.5-4.5-10.6-7-17-7H64z M144 208a32 32 0 1 1 64 0 32 32 0 1 1 -64 0zm120 0c0-13.3 10.7-24 24-24H360c13.3 0 24 10.7 24 24s-10.7 24-24 24H288c-13.3 0-24-10.7-24-24zm0 96c0-13.3 10.7-24 24-24H360c13.3 0 24 10.7 24 24s-10.7 24-24 24H288c-13.3 0-24-10.7-24-24zm-120 0a32 32 0 1 1 64 0 32 32 0 1 1 -64 0z"/></svg></div>
                    <h3 class="feature-title">Doorzoekbaar <a href="https://preektekst.wesotronic.nl" target="_blank" rel="noopener">Archief</a></h3>
                    <p>Alle preken worden veilig opgeslagen. Vind eenvoudig passages terug op onderwerp, datum of spreker in het <a href="https://preektekst.wesotronic.nl" target="_blank" rel="noopener">archief</a>.</p>
                </div>

                <div class="feature-card animate-on-scroll fade-in delay-400">
                    <div class="feature-icon"><svg xmlns="http://www.w3.org/2000/svg" height="1em" viewBox="0 0 512 512"><path d="M78.6 5C69.1-2.4 55.6-1.5 47 7L7 47c-8.5 8.5-9.4 22-2.1 31.6l80 104c4.5 5.9 11.6 9.4 19 9.4h54.1l109 109c-14.7 29-10 65.4 14.3 89.6l112 112c12.5 12.5 32.8 12.5 45.3 0l64-64c12.5-12.5 12.5-32.8 0-45.3l-112-112c-24.2-24.2-60.6-29-89.6-14.3l-109-109V104c0-7.5-3.5-14.5-9.4-19L78.6 5zM19.9 396.1C7.2 408.8 0 426.1 0 444.1C0 481.6 30.4 512 67.9 512c18 0 35.3-7.2 48-19.9L233.7 374.3c-7.8-20.9-9-43.6-3.6-65.1l-61.7-61.7L19.9 396.1zM512 144c0-10.5-1.1-20.7-3.2-30.5c-2.4-11.2-16.1-14.1-24.2-6l-63.9 63.9c-3 3-7.1 4.7-11.3 4.7H352c-8.8 0-16-7.2-16-16V102.6c0-4.2 1.7-8.3 4.7-11.3l63.9-63.9c8.1-8.1 5.2-21.8-6-24.2C388.7 1.1 378.5 0 368 0C288.5 0 224 64.5 224 144l0 .8 85.3 85.3c36-9.1 75.8 .5 104 28.7L429 274.5c49-23 83-72.8 83-130.5zM56 432a24 24 0 1 1 48 0 24 24 0 1 1 -48 0z"/></svg></div>
                    <h3 class="feature-title">Eenvoudige Installatie</h3>
                    <p>Minimale hardware vereist. Werkt met de meeste bestaande geluidsinstallaties. Snelle setup.</p>
                </div>

                <div class="feature-card animate-on-scroll fade-in delay-500">
                     <div class="feature-icon"><svg xmlns="http://www.w3.org/2000/svg" height="1em" viewBox="0 0 448 512"><path d="M144 144v48H304V144c0-44.2-35.8-80-80-80s-80 35.8-80 80zM80 144v48H16c-8.8 0-16 7.2-16 16v32c0 8.8 7.2 16 16 16H64V416c0 53 43 96 96 96H288c53 0 96-43 96-96V256h48c8.8 0 16-7.2 16-16V208c0-8.8-7.2-16-16-16H368V144C368 64.5 303.5 0 224 0S80 64.5 80 144zM256 416H192c-8.8 0-16-7.2-16-16s7.2-16 16-16h64c8.8 0 16 7.2 16 16s-7.2 16-16 16zm-32-120c13.3 0 24 10.7 24 24s-10.7 24-24 24s-24-10.7-24-24s10.7-24 24-24z"/></svg></div>
                    <h3 class="feature-title">Privacy & Veiligheid</h3>
                    <p>Gegevens worden veilig opgeslagen conform AVG. U bepaalt wie toegang heeft tot transcripties en archieven.</p>
                </div>
            </div>

            <div class="counter-container animate-on-scroll fade-in">
                <div class="counter-item">
                    <div class="counter" data-target="36">0+</div>
                    <div class="counter-title">Kerken geholpen</div>
                </div>
                <div class="counter-item">
                    <div class="counter" data-target="3000">0+</div>
                    <div class="counter-title">Diensten getranscribeerd</div>
                </div>
                <div class="counter-item">
                    <div class="counter" data-target="200">0+</div>
                    <div class="counter-title">Beschikbare talen</div>
                </div>
                <div class="counter-item">
                    <div class="counter" data-target="98">0%</div>
                    <div class="counter-title">Klanttevredenheid</div>
                </div>
            </div>
        </div>
    </section>
        <!-- ==========   NIEUW!  Waarom Preeklezen?  ========== -->
<section id="why-preeklezen" class="section bg-light">
    <div class="container">
      <div class="section-header animate-on-scroll fade-in">
        <p class="section-subheading">Waarom wij anders zijn</p>
        <h2>Preeklezen ten opzichte van andere aanbieders</h2>
        <p class="lead">We leveren aantoonbaar betere resultaten voor minder geld –
          en verbeteren ons product continu.</p>
      </div>

      <div class="features-grid">
        <!-- SNELLER -->
        <div class="feature-card animate-on-scroll fade-in">
          <div class="feature-icon">
            <!-- stopwatch icoon -->
            <svg xmlns="http://www.w3.org/2000/svg" height="1em" viewBox="0 0 512 512"><path d="M464 256A208 208 0 1 1 48 256a208 208 0 1 1 416 0zM0 256a256 256 0 1 0 512 0A256 256 0 1 0 0 256zM232 120V256c0 8 4 15.5 10.7 20l96 64c11 7.4 25.9 4.4 33.3-6.7s4.4-25.9-6.7-33.3L280 243.2V120c0-13.3-10.7-24-24-24s-24 10.7-24 24z"/></svg>
          </div>
          <h3 class="feature-title">Zeer snelle verwerking</h3>
          <p>Onze end‑to‑end vertraging is minimaal, omdat we lokaal
            audio verwerken en streamen. We gebruiken de nieuwste
            technologieën om de snelheid te maximaliseren.</p>
          </p>
        </div>

        <!-- GOEDKOPER -->
        <div class="feature-card animate-on-scroll fade-in delay-100">
          <div class="feature-icon">
            <!-- euro icoon -->
            <svg xmlns="http://www.w3.org/2000/svg" height="1em" viewBox="0 0 384 512"><path d="M64 240c0 5.5 .2 11 .6 16H24c-13.3 0-24 10.7-24 24s10.7 24 24 24H73.2c15.2 54.8 64.3 96.3 123.4 99.6c-7.9 15.8-15.2 32.4-21.8 49.5c-5.6 14.5 1.7 30.8 16.2 36.4s30.8-1.7 36.4-16.2c7.2-18.7 15.1-36.8 23.7-54.2H312c13.3 0 24-10.7 24-24s-10.7-24-24-24H271.8c7.9-16.7 15.2-33.8 21.8-51.3c5.7-15.1-2-32-17.1-37.7s-32 2-37.7 17.1c-5.6 14.9-11.8 29.4-18.6 43.6c-41.7-1.2-77.4-26.3-90.8-61.7H312c13.3 0 24-10.7 24-24s-10.7-24-24-24H121.7c-.4-5.3-.6-10.6-.6-16s.2-10.7 .6-16H312c13.3 0 24-10.7 24-24s-10.7-24-24-24H128.6c13.4-35.4 49.1-60.5 90.8-61.7c6.8 14.1 13.1 28.7 18.6 43.6c5.7 15.1 22.6 22.8 37.7 17.1s22.8-22.6 17.1-37.7c-6.6-17.5-13.9-34.6-21.8-51.3H312c13.3 0 24-10.7 24-24s-10.7-24-24-24H252.3c-8.6-17.5-16.5-35.6-23.7-54.2c-5.6-14.5-21.9-21.8-36.4-16.2s-21.8 21.9-16.2 36.4c6.6 17.1 13.9 33.7 21.8 49.5C138.4 48.3 89.3 89.9 73.2 144H24c-13.3 0-24 10.7-24 24s10.7 24 24 24H64z"/></svg>
          </div>
          <h3 class="feature-title">Tot 70 % goedkoper</h3>
          <p>Geen extra kosten per dienst of per bezoeker. Één flat‑fee
            abonnement dat gemiddeld de helft lager ligt dan
            concurrerende oplossingen.</p>
        </div>

        <!-- BETERE KWALITEIT -->
        <div class="feature-card animate-on-scroll fade-in delay-200">
          <div class="feature-icon">
            <!-- trophy icoon -->
            <svg xmlns="http://www.w3.org/2000/svg" height="1em" viewBox="0 0 576 512"><path d="M400 0H176c-26.5 0-48.1 21.8-47.1 48.2c.2 5.3 .4 10.6 .7 15.8H24C10.7 64 0 74.7 0 88c0 92.6 33.5 157 78.5 200.7c44.3 43.1 98.3 64.8 138.1 75.8c23.4 6.5 39.4 26 39.4 45.6c0 20.9-17 37.9-37.9 37.9H192c-17.7 0-32 14.3-32 32s14.3 32 32 32H384c17.7 0 32-14.3 32-32s-14.3-32-32-32H357.9C337 448 320 431 320 410.1c0-19.6 15.9-39.2 39.4-45.6c39.9-11 93.9-32.7 138.2-75.8C542.5 245 576 180.6 576 88c0-13.3-10.7-24-24-24H446.4c.3-5.2 .5-10.4 .7-15.8C448.1 21.8 426.5 0 400 0zM48.9 112h84.4c9.1 90.1 29.2 150.3 51.9 190.6c-24.9-11-50.8-26.5-73.2-48.3c-32-31.1-58-76-63-142.3zM464.1 254.3c-22.4 21.8-48.3 37.3-73.2 48.3c22.7-40.3 42.8-100.5 51.9-190.6h84.4c-5.1 66.3-31.1 111.2-63 142.3z"/></svg>
          </div>
          <h3 class="feature-title">Meest nauwkeurige transcriptie</h3>
          <p>AI‑modellen getraind op theologische context leveren tot 99 %
            accuraatheid – en leren wekelijks bij op basis van gebruikers­
            feedback en ontwikkelingen.</p>
        </div>

        <!-- ARCHIEF -->
        <div class="feature-card animate-on-scroll fade-in delay-300">
          <div class="feature-icon">
            <!-- archive icoon -->
            <svg xmlns="http://www.w3.org/2000/svg" height="1em" viewBox="0 0 512 512"><path d="M64 480H448c35.3 0 64-28.7 64-64V160c0-35.3-28.7-64-64-64H288c-10.1 0-19.6-4.7-25.6-12.8L243.2 57.6C231.1 41.5 212.1 32 192 32H64C28.7 32 0 60.7 0 96V416c0 35.3 28.7 64 64 64z"/></svg>
          </div>
          <h3 class="feature-title">Leespreek uitdraai</h3>
          <p>Ieder gesproken woord wordt omgezet naar een stijlvolle <a href="https://preektekst.wesotronic.nl" target="_blank" rel="noopener">leespreek</a> – ideaal voor predikanten of thuisstudie.</p>
        </div>

        <!-- INNOVATIE -->
        <div class="feature-card animate-on-scroll fade-in delay-400">
          <div class="feature-icon">
            <!-- lightbulb icoon -->
            <svg xmlns="http://www.w3.org/2000/svg" height="1em" viewBox="0 0 384 512"><path d="M272 384c9.6-31.9 29.5-59.1 49.2-86.2l0 0c5.2-7.1 10.4-14.2 15.4-21.4c19.8-28.5 31.4-63 31.4-100.3C368 78.8 289.2 0 192 0S16 78.8 16 176c0 37.3 11.6 71.9 31.4 100.3c5 7.2 10.2 14.3 15.4 21.4l0 0c19.8 27.1 39.7 54.4 49.2 86.2H272zM192 512c44.2 0 80-35.8 80-80V416H112v16c0 44.2 35.8 80 80 80zM112 176c0 8.8-7.2 16-16 16s-16-7.2-16-16c0-61.9 50.1-112 112-112c8.8 0 16 7.2 16 16s-7.2 16-16 16c-44.2 0-80 35.8-80 80z"/></svg>
          </div>
          <h3 class="feature-title">Regelmatig nieuwe functies</h3>
          <p>Ons team rolt continu verbeteringen uit, rechtstreeks
            gebaseerd op feedback vanuit gemeenten.</p>
        </div>
      </div>
    </div>
  </section>

    <section id="how-it-works" class="section bg-light church-how-it-works-bg">
        <div class="container">
            <div class="section-header animate-on-scroll fade-in">
                <p class="section-subheading">Eenvoudig proces</p>
                <h2>Hoe Preeklezen werkt in 4 stappen</h2>
                <p class="lead">Implementeer Preeklezen moeiteloos in uw kerk en begin direct met het vergroten van de toegankelijkheid.</p>
            </div>

            <div class="steps">
                <div class="step animate-on-scroll fade-in">
                    <div class="step-number">1</div>
                    <div class="step-content">
                        <h3 class="step-title">Aanmelden & Installatie</h3>
                        <p>Kies een pakket en meld u aan. Installatie kost eenmalig €540. Deze prijs is gebaseerd op een locatie in een straal van 100km van Barendrecht.</p>
                    </div>
                </div>

                <div class="step animate-on-scroll fade-in delay-100">
                    <div class="step-number">2</div>
                    <div class="step-content">
                        <h3 class="step-title">Koppelen Geluidssysteem</h3>
                        <p>Wij installeren de benodigde hardware en software en configureren dit op locatie met het netwerk en de geluidsinstallatie. Met deze configuratie zorgen we voor zo min mogelijk vertraging in de transcriptie.</p>
                    </div>
                </div>

                <div class="step animate-on-scroll fade-in delay-200">
                    <div class="step-number">3</div>
                    <div class="step-content">
                        <h3 class="step-title">Live Transcriptie & Vertaling</h3>
                        <p>Alles start automatisch voor de dienst. Dit is direct zichtbaar op de telefoon of tablet van de gebruiker bij het bezoeken van de website.</p>
                    </div>
                </div>

                <div class="step animate-on-scroll fade-in delay-300">
                    <div class="step-number">4</div>
                    <div class="step-content">
                        <h3 class="step-title">Archief & Analyse</h3>
                        <p>Na de dienst kan de beheerder de <a href="https://preektekst.wesotronic.nl" target="_blank" rel="noopener">transcriptie</a> beschikbaar maken in de cloud. Hierna is de <a href="https://preektekst.wesotronic.nl" target="_blank" rel="noopener">transcriptie</a> te bekijken of te downloaden. Deze <a href="https://preektekst.wesotronic.nl" target="_blank" rel="noopener">transcriptie</a> is in de cloud met een klik op de knop om te zetten van spreektaal naar een volledig in stijl gemaakte <a href="https://preektekst.wesotronic.nl" target="_blank" rel="noopener">leespreek</a>.</p>
                    </div>
                </div>
            </div>

            <div class="text-center mt-5 animate-on-scroll fade-in delay-400">
                <a href="#demo" class="btn btn-primary btn-lg">Bekijk hoe preeklezen te werk gaat</a>
            </div>
        </div>
    </section>


    <section id="demo" class="section">
        <div class="container">
            <div class="section-header animate-on-scroll fade-in">
                <p class="section-subheading"></p>
                <h2>Preeklezen in de praktijk</h2>
                <p class="lead">Ervaar zelf hoe de verschillende onderdelen van Preeklezen werken.</p>
            </div>

            <div class="demo-tabs animate-on-scroll fade-in">
                <div class="demo-tab active" data-target="demo-transcriptie">Live Transcriptie</div>
                <div class="demo-tab" data-target="demo-vertaling">Meertalige Vertaling</div>
                <div class="demo-tab" data-target="demo-dashboard">Beheer Dashboard</div>
            </div>

            <div class="demo-content-container">
                <div id="demo-transcriptie" class="demo-content active animate-on-scroll fade-in">
                    <h3 class="text-center mb-4">Live Transcriptie op elk apparaat</h3>
                    <p class="text-center mb-4">De preek wordt real-time omgezet naar tekst. Bezoekers lezen mee op hun eigen apparaat en kunnen lettergrootte en contrast aanpassen.</p>
                    <div class="demo-image">
                        <video id="video-transcriberen" width="900" height="500" preload="auto" loop playsinline style="display: block; margin: 0 auto; border-radius: var(--border-radius); box-shadow: var(--shadow-md);">
  <source src="transcriberen.mp4" type="video/mp4">
  Uw browser ondersteunt de video tag niet.
</video>
                    </div>
                </div>

                <div id="demo-vertaling" class="demo-content animate-on-scroll fade-in">
                     <h3 class="text-center mb-4">Direct beschikbaar in 200+ talen</h3>
                     <p class="text-center mb-4">Selecteer eenvoudig een taal in de app en de tekst wordt direct vertaald. Onze AI houdt rekening met theologische context voor nauwkeurigere vertalingen.</p>
                    <div class="demo-image">
                         <video id="video-vertalen" width="900" height="500" preload="auto" loop playsinline style="display: block; margin: 0 auto; border-radius: var(--border-radius); box-shadow: var(--shadow-md);">
  <source src="vertalen.mp4" type="video/mp4">
  Uw browser ondersteunt de video tag niet.
</video>
                    </div>
                </div>

                <div id="demo-dashboard" class="demo-content animate-on-scroll fade-in">
                    <h3 class="text-center mb-4">Uitgebreid Beheer Dashboard</h3>
                    <p class="text-center mb-4">Beheer eenvoudig uw <a href="https://preektekst.wesotronic.nl" target="_blank" rel="noopener">archief</a>, bewerk <a href="https://preektekst.wesotronic.nl" target="_blank" rel="noopener">transcripties</a> en bekijk statistieken via het online <a href="https://preektekst.wesotronic.nl" target="_blank" rel="noopener">dashboard</a>.</p>
                    <div class="demo-image">
                         <!-- Replace the placeholder image with the video tag -->
                         <video id="video-dashboard" width="900" height="500" preload="auto" loop playsinline style="display: block; margin: 0 auto; border-radius: var(--border-radius); box-shadow: var(--shadow-md);">
  <source src="Preektekst.mp4" type="video/mp4">
  Uw browser ondersteunt de video tag niet.
</video>
                    </div>
                </div>
            </div>
        </div>
    </section>


    <section id="pricing" class="section bg-light">
        <div class="container">
            <div class="section-header animate-on-scroll fade-in">
                <p class="section-subheading">Duidelijke investering</p>
                <h2>Transparante Prijzen</h2>
                <p class="lead">Kies het pakket dat het beste bij de behoeften van uw gemeente past. Maandelijks opzegbaar.</p>
            </div>

            <div class="pricing-cards">
                <div class="pricing-card animate-on-scroll fade-in">
                    <div class="pricing-header">
                        <h3 class="pricing-name">Basis</h3>
                        <div class="pricing-price">€27,50<small>*</small></div>
                        <div class="pricing-duration">per maand</div>
                    </div>
                    <ul class="pricing-features">
                        <li>Live transcriptie</li>
                        <li>Live Vertaling (200+ talen)</li>
                        <li>Web app toegang</li>
                        <li>Max. 2 gelijktijdige talen</li>
                        <li>~10 diensten per maand</li>
                        <li>E-mail support</li>
                    </ul>
                    <a href="#contact" class="btn btn-outline mt-4">Vraag basis aan</a>
                </div>

                <div class="pricing-card featured animate-on-scroll fade-in delay-100">
                    <div class="pricing-header">
                        <h3 class="pricing-name">Compleet</h3>
                        <div class="pricing-price">€45<small>*</small></div>
                        <div class="pricing-duration">per maand</div>
                    </div>
                    <ul class="pricing-features">
                        <li>Live transcriptie</li>
                        <li>Live Vertaling (200+ talen)</li>
                        <li>Web app toegang</li>
                        <li><strong>Onbeperkte talen</strong></li>
                        <li><strong>12 maanden <a href="https://preektekst.wesotronic.nl" target="_blank" rel="noopener">archief</a></strong></li>
                        <li><a href="https://preektekst.wesotronic.nl" target="_blank" rel="noopener">Doorzoekbaar archief</a></li>
                        <li><strong>Op afstand meelezen</strong></li>
                        <li>Prioriteit support</li>
                    </ul>
                    <a href="#contact" class="btn btn-primary mt-4">Vraag compleet aan</a>
                </div>

                <div class="pricing-card animate-on-scroll fade-in delay-200">
                    <div class="pricing-header">
                        <h3 class="pricing-name">Maatwerk</h3>
                        <div class="pricing-price">Op aanvraag</div>
                        <div class="pricing-duration">Neem contact op</div>
                    </div>
                    <ul class="pricing-features">
                        <li>Alle 'Compleet' functies</li>
                        <li>Integratie met kerkwebsite/app</li>
                        <li>Eigen branding</li>
                        <li><strong>Flexibele wensen</strong></li>
                    </ul>
                    <a href="#contact" class="btn btn-outline mt-4">Vraag offerte aan</a>
                </div>
            </div>

             <div class="installation-cost animate-on-scroll fade-in delay-300">
                 <h4>Eenmalige Installatiekosten</h4>
                 <p>Voor de levering en configuratie van de benodigde Preeklezen-unit rekenen wij eenmalige kosten.</p>
                 <p><strong>€ 540,-</strong><small>*</small></p>
                 <p class="text-muted"><small>Dit omvat de hardware en basisconfiguratie. Exclusief eventuele extra bekabeling of aanpassingen aan uw bestaande geluidssysteem.</small></p>
                 <p class="text-muted"><small>*prijzen zijn excl. BTW</small></p>
            </div>

            <div class="quote text-center mt-5 animate-on-scroll fade-in delay-400">
                "De maandelijkse abonnementskosten zijn de enige terugkerende kosten. Geen verborgen toeslagen. U kunt maandelijks opzeggen."
            </div>
        </div>
    </section>


    <section id="testimonials" class="section testimonials">
        <div class="testimonials-bg church-testimonials-bg"></div>
        <div class="container">
            <div class="section-header animate-on-scroll fade-in">
                <p class="section-subheading">Wat kerken zeggen</p>
                <h2>Ervaringen van Gebruikers</h2>
                <p class="lead">Lees hoe Preeklezen gemeenten helpt om diensten toegankelijker te maken voor iedereen.</p>
            </div>

            <div class="testimonials-slider">
                <div class="testimonial-card animate-on-scroll fade-in">
                    <div class="testimonial-content">
                        "Een uitkomst voor onze slechthorende leden! De live transcriptie werkt uitstekend en de app is zeer gebruiksvriendelijk."
                    </div>
                </div>

                <div class="testimonial-card animate-on-scroll fade-in delay-100">
                    <div class="testimonial-content">
                       "De AI-vertaling is indrukwekkend nauwkeurig, zelfs met theologische termen. Onze internationale bezoekers voelen zich nu veel meer betrokken."
                    </div>
                </div>

                <div class="testimonial-card animate-on-scroll fade-in delay-200">
                    <div class="testimonial-content">
                       "Het <a href="https://preektekst.wesotronic.nl" target="_blank" rel="noopener">doorzoekbare archief</a> is goud waard voor preekvoorbereiding en het terugvinden van specifieke onderwerpen. Bespaart ons veel tijd."
                    </div>
                </div>

                 <div class="testimonial-card animate-on-scroll fade-in delay-300">
                    <div class="testimonial-content">
                       "De installatie was eenvoudiger dan verwacht en de support van Preeklezen is snel en deskundig. Een aanrader!"
                    </div>
                </div>
                 <div class="testimonial-card animate-on-scroll fade-in delay-400">
                    <div class="testimonial-content">
                       "Niet alleen voor slechthorenden, ook voor de dominee ontzettend fijn om de preek terug te kunnen lezen en te delen."
                    </div>
                </div>
            </div>
        </div>
    </section>


    <section id="faq" class="section church-faq-bg">
        <div class="container">
            <div class="section-header animate-on-scroll fade-in">
                <p class="section-subheading">Veelgestelde vragen</p>
                <h2>Antwoorden op uw vragen</h2>
                <p class="lead">Vind hier snel antwoord op de meest voorkomende vragen over Preeklezen.</p>
            </div>

            <style>
                .faq-list {
                    max-width: 900px;
                    margin: 0 auto;
                }
                .faq-question {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }
                .faq-question span:first-child {
                    flex: 1;
                }
                .faq-icon {
                    flex: 0 0 20px;
                    text-align: center;
                }
            </style>
            <div class="faq-list">
                <div class="faq-item animate-on-scroll fade-in">
                    <div class="faq-question">
                        <span>Hoe nauwkeurig is de transcriptie?</span>
                        <span class="faq-icon">+</span>
                    </div>
                    <div class="faq-answer">
                        <p>Onze AI bereikt doorgaans een nauwkeurigheid van 90-98%, afhankelijk van de geluidskwaliteit, articulatie van de spreker en achtergrondgeluid. Het systeem leert continu en kan worden getraind op specifiek jargon of namen binnen uw gemeente voor nog betere resultaten.</p>
                    </div>
                </div>

                <div class="faq-item animate-on-scroll fade-in delay-100">
                    <div class="faq-question">
                        <span>Wat hebben we nodig voor de installatie?</span>
                         <span class="faq-icon">+</span>
                    </div>
                    <div class="faq-answer">
                        <p>U heeft nodig: (1) De Preeklezen-unit (die wij leveren), (2) Een stabiele internetverbinding (bekabeld of WiFi) in de kerkzaal, (3) Een audio-uitgang van uw geluidsmixer (bijv. 'line out' of 'aux out'). Wij leveren de benodigde basiskabels. Gemeenteleden gebruiken hun eigen apparaten (smartphone/tablet) met een webbrowser.</p>
                    </div>
                </div>

                 <div class="faq-item animate-on-scroll fade-in delay-200">
                    <div class="faq-question">
                        <span>Hoe werkt de vertaalfunctie precies?</span>
                         <span class="faq-icon">+</span>
                    </div>
                    <div class="faq-answer">
                        <p>De live transcriptie wordt eerst in het Nederlands gegenereerd. Vervolgens wordt deze tekst door onze AI-vertaalengine omgezet naar de door de gebruiker geselecteerde taal. Dit gebeurt met een zeer geringe vertraging na de Nederlandse tekst. We ondersteunen meer dan 200 talen, waaronder Engels, Duits, Frans, Spaans, Pools, Arabisch, Oekraïens en meer.</p>
                    </div>
                </div>

                <div class="faq-item animate-on-scroll fade-in delay-300">
                    <div class="faq-question">
                        <span>Kunnen we de transcripties achteraf bewerken?</span>
                         <span class="faq-icon">+</span>
                    </div>
                    <div class="faq-answer">
                        <p>Ja, via het online Preeklezen <a href="https://preektekst.wesotronic.nl" target="_blank" rel="noopener">dashboard</a> heeft u toegang tot alle gearchiveerde <a href="https://preektekst.wesotronic.nl" target="_blank" rel="noopener">transcripties</a>. U kunt hier eenvoudig de tekst inzien en kopiëren naar je meest gebruikte tekstverwerker.</p>
                    </div>
                </div>

                 <div class="faq-item animate-on-scroll fade-in delay-400">
                    <div class="faq-question">
                        <span>Is Preeklezen ook geschikt voor andere evenementen?</span>
                         <span class="faq-icon">+</span>
                    </div>
                    <div class="faq-answer">
                        <p>Absoluut! Hoewel primair ontworpen voor kerkdiensten, kan Preeklezen ook uitstekend gebruikt worden voor lezingen, conferenties, vergaderingen, seminars of andere evenementen waar live transcriptie of vertaling gewenst is.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>


    <section id="contact" class="section">
        <div class="container animate-on-scroll fade-in">
          <div class="section-header text-center">
            <p class="section-subheading">Laat van u horen</p>
            <h2>Contact & offerte‑aanvraag</h2>
            <p class="lead">Vul onderstaand formulier in en wij reageren
              doorgaans binnen één werkdag.</p>
          </div>

          <form class="contact-form" action="https://formspree.io/f/mnndqqqa" method="POST">
            <div class="form-group">
              <label class="form-label" for="name">Naam</label>
              <input required type="text" id="name" name="name" class="form-control" placeholder="Uw naam">
            </div>
            <div class="form-group">
              <label class="form-label" for="church">Kerk / Organisatie</label>
              <input type="text" id="church" name="church" class="form-control" placeholder="Naam kerk of organisatie">
            </div>
            <div class="form-group">
              <label class="form-label" for="email">E‑mail</label>
              <input required type="email" id="email" name="_replyto" class="form-control" placeholder="<EMAIL>">
            </div>
            <div class="form-group">
              <label class="form-label" for="phone">Telefoon (optioneel)</label>
              <input type="tel" id="phone" name="phone" class="form-control" placeholder="06‑12345678">
            </div>
            <div class="form-group">
              <label class="form-label" for="message">Bericht / Vraag</label>
              <textarea required id="message" name="message" class="form-control" placeholder="Vertel ons kort waar we u mee kunnen helpen"></textarea>
            </div>
            <div class="form-group">
              <label>
                <input required type="checkbox" name="privacy"> Ik ga akkoord met de
                <a href="#privacy">privacyverklaring</a>.
              </label>
            </div>
            <button class="btn btn-primary btn-lg" type="submit">Verstuur bericht</button>
          </form>

          <p class="text-center mt-4">
            Liever direct mailen? <a href="mailto:<EMAIL>"><EMAIL></a> – of bel
            <a href="tel:0180-692004">0180-692004</a>
          </p>
        </div>
      </section>


    <footer class="footer">
        <div class="container">
            <div class="footer-grid">
                <div class="footer-company">
                     <a href="#home" class="footer-logo">
                         <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 200 60'%3E%3Crect width='200' height='60' fill='transparent'/%3E%3Ctext x='10' y='40' font-family='Arial' font-size='20' fill='rgba(255,255,255,0.8)'%3EPreeklezen.nl%3C/text%3E%3C/svg%3E" alt="Preeklezen Logo Wit">
                     </a>
                    <p>Live transcriptie en vertaling voor kerken en evenementen. Powered by Wesotronic.</p>
                    <div class="footer-social">
                        <a href="https://www.linkedin.com/company/wesotronic-av-solutions/posts/?feedView=all" class="social-icon" aria-label="LinkedIn"><svg xmlns="http://www.w3.org/2000/svg" height="1em" viewBox="0 0 448 512"><path d="M416 32H31.9C14.3 32 0 46.5 0 64.3v383.4C0 465.5 14.3 480 31.9 480H416c17.6 0 32-14.5 32-32.3V64.3c0-17.8-14.4-32.3-32-32.3zM135.4 416H69V202.2h66.5V416zm-33.2-243c-21.3 0-38.5-17.3-38.5-38.5S80.9 96 102.2 96c21.2 0 38.5 17.3 38.5 38.5 0 21.3-17.2 38.5-38.5 38.5zm282.1 243h-66.4V312c0-24.8-.5-56.7-34.5-56.7-34.6 0-39.9 27-39.9 54.9V416h-66.4V202.2h63.7v29.2h.9c8.9-16.8 30.6-34.5 62.9-34.5 67.2 0 79.7 44.3 79.7 101.9V416z"/></svg></a>
                        <a href="mailto:<EMAIL>" class="social-icon" aria-label="Email"><svg xmlns="http://www.w3.org/2000/svg" height="1em" viewBox="0 0 512 512"><path d="M48 64C21.5 64 0 85.5 0 112c0 15.1 7.1 29.3 19.2 38.4L236.8 313.6c11.4 8.5 27 8.5 38.4 0L492.8 150.4c12.1-9.1 19.2-23.3 19.2-38.4c0-26.5-21.5-48-48-48H48zM0 176V384c0 35.3 28.7 64 64 64H448c35.3 0 64-28.7 64-64V176L294.4 339.2c-22.8 17.1-54 17.1-76.8 0L0 176z"/></svg></a>
                    </div>
                </div>
                <div>
                    <h4 class="footer-heading">Snelle Links</h4>
                    <ul class="footer-links">
                        <li><a href="#features">Functies</a></li>
                        <li><a href="#how-it-works">Hoe het werkt</a></li>
                        <li><a href="#pricing">Prijzen</a></li>
                        <li><a href="#demo">Demo</a></li>
                        <li><a href="#faq">FAQ</a></li>
                    </ul>
                </div>
                 <div>
                    <h4 class="footer-heading">Ondersteuning</h4>
                    <ul class="footer-links">
                        <li><a href="#contact">Contact Opnemen</a></li>
                        <li><a href="#">Documentatie</a></li>
                        <li><a href="#">Status Pagina</a></li>
                        <li><a href="#">Privacybeleid</a></li>
                        <li><a href="#">Algemene Voorwaarden</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="footer-heading">Contact</h4>
                     <ul class="footer-links">
                        <li class="footer-contact-item">
                           <span class="contact-icon"><svg xmlns="http://www.w3.org/2000/svg" height="1em" viewBox="0 0 384 512"><path d="M172.268 501.67C26.97 291.031 0 269.413 0 192 0 85.961 85.961 0 192 0s192 85.961 192 192c0 77.413-26.97 99.031-172.268 309.67a24 24 0 0 1-35.464 0zM192 272c44.183 0 80-35.817 80-80s-35.817-80-80-80-80 35.817-80 80 35.817 80 80 80z"/></svg></span>
                            <span>Ebweg 10<br>2991 LT Barendrecht</span>
                        </li>
                         <li class="footer-contact-item">
                             <span class="contact-icon"><svg xmlns="http://www.w3.org/2000/svg" height="1em" viewBox="0 0 512 512"><path d="M48 64C21.5 64 0 85.5 0 112c0 15.1 7.1 29.3 19.2 38.4L236.8 313.6c11.4 8.5 27 8.5 38.4 0L492.8 150.4c12.1-9.1 19.2-23.3 19.2-38.4c0-26.5-21.5-48-48-48H48zM0 176V384c0 35.3 28.7 64 64 64H448c35.3 0 64-28.7 64-64V176L294.4 339.2c-22.8 17.1-54 17.1-76.8 0L0 176z"/></svg></span>
                             <a href="mailto:<EMAIL>"><EMAIL></a>
                        </li>
                        <li class="footer-contact-item">
                             <span class="contact-icon"><svg xmlns="http://www.w3.org/2000/svg" height="1em" viewBox="0 0 512 512"><path d="M164.9 24.6c-7.7-18.6-28-28.5-47.4-23.2l-88 24C12.1 30.2 0 46 0 64C0 311.4 200.6 512 448 512c18 0 33.8-12.1 38.6-29.5l24-88c5.3-19.4-4.6-39.7-23.2-47.4l-96-40c-16.3-6.8-35.2-2.1-46.3 11.6L304.7 368C234.3 334.7 177.3 277.7 144 207.3L193.3 167c13.7-11.2 18.4-30 11.6-46.3l-40-96z"/></svg></span>
                             <a href="tel:0180692004">0180-692004</a>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                &copy; <span id="current-year"></span> Preeklezen.nl - Een dienst van Wesotronic. Alle rechten voorbehouden.
            </div>
        </div>
    </footer>

    <script>

        document.addEventListener('DOMContentLoaded', function() {


            const menuBtn = document.getElementById('mobile-menu-btn');
            const navLinks = document.getElementById('nav-links');
            if (menuBtn && navLinks) {
                menuBtn.addEventListener('click', () => {
                    navLinks.classList.toggle('active');
                    menuBtn.classList.toggle('active');
                    const isExpanded = menuBtn.getAttribute('aria-expanded') === 'true';
                    menuBtn.setAttribute('aria-expanded', !isExpanded);

                    document.body.style.overflow = navLinks.classList.contains('active') ? 'hidden' : '';
                });


                navLinks.querySelectorAll('a').forEach(link => {
                    link.addEventListener('click', () => {
                        if (navLinks.classList.contains('active')) {
                            navLinks.classList.remove('active');
                            menuBtn.classList.remove('active');
                            menuBtn.setAttribute('aria-expanded', 'false');
                            document.body.style.overflow = '';
                        }
                    });
                });
            }


            const header = document.getElementById('header');
            if (header) {
                window.addEventListener('scroll', () => {
                    if (window.scrollY > 50) {
                        header.classList.add('scrolled');
                    } else {
                        header.classList.remove('scrolled');
                    }
                });
            }


            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    const href = this.getAttribute('href');

                    if (href.length > 1 && document.querySelector(href)) {
                        e.preventDefault();
                        const targetElement = document.querySelector(href);
                        const headerOffset = document.getElementById('header') ? document.getElementById('header').offsetHeight : 70;
                        const elementPosition = targetElement.getBoundingClientRect().top;
                        const offsetPosition = elementPosition + window.pageYOffset - headerOffset;

                        window.scrollTo({
                            top: offsetPosition,
                            behavior: "smooth"
                        });


                        document.querySelectorAll('.nav-links a').forEach(link => link.classList.remove('active'));
                        this.classList.add('active');


                        if (navLinks && navLinks.classList.contains('active')) {
                             navLinks.classList.remove('active');
                             menuBtn.classList.remove('active');
                             menuBtn.setAttribute('aria-expanded', 'false');
                             document.body.style.overflow = '';
                        }
                    }
                });
            });


            const faqItems = document.querySelectorAll('.faq-item');
            faqItems.forEach(item => {
                const question = item.querySelector('.faq-question');
                if (question) {
                    question.addEventListener('click', () => {
                        const currentlyActive = document.querySelector('.faq-item.active');


                        item.classList.toggle('active');
                    });
                }
            });





            const animatedElements = document.querySelectorAll('.animate-on-scroll');
            if ("IntersectionObserver" in window) {
                const observer = new IntersectionObserver((entries, observer) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            entry.target.classList.add('is-visible');


                        } else {


                        }
                    });
                }, { threshold: 0.1 });

                animatedElements.forEach(el => observer.observe(el));
            } else {

                animatedElements.forEach(el => el.classList.add('is-visible'));
            }



            const counters = document.querySelectorAll('.counter');
            const speed = 200;

            const animateCounter = (counter) => {
                const target = +counter.getAttribute('data-target');
                const count = +counter.innerText.replace(/[^0-9]/g,'');

                const increment = target / speed;

                if (count < target) {
                    counter.innerText = Math.ceil(count + increment) + (counter.innerText.includes('%') ? '%' : (counter.innerText.includes('+') ? '+' : ''));
                    setTimeout(() => animateCounter(counter), 10);
                } else {
                    counter.innerText = target + (counter.innerText.includes('%') ? '%' : (counter.innerText.includes('+') ? '+' : ''));
                }
            };

             if ("IntersectionObserver" in window) {
                const counterObserver = new IntersectionObserver((entries, observer) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting && !entry.target.classList.contains('counted')) {
                            animateCounter(entry.target);
                            entry.target.classList.add('counted');


                        }
                    });
                }, { threshold: 0.5 });

                counters.forEach(counter => counterObserver.observe(counter));
            } else {

                 counters.forEach(counter => animateCounter(counter));
            }
            const baseDate = new Date('2024-01-14T00:00:00');          // eerste zondag met 3000 diensten
  const baseCount = 0;                                    // startwaarde
  const perSunday = 70;                                      // groei per week

  function getTranscriptionTarget() {
    const now = new Date();
    // aantal hele zondagen sinds baseDate
    const msPerWeek = 1000 * 60 * 60 * 24 * 7;
    const weeks = Math.floor((now - baseDate) / msPerWeek);
    return baseCount + (weeks * perSunday);
  }

  // zet het data‑target attribuut vóór de teller‑animatie start
  const transcriptionCounter = document.querySelector('.counter[data-target="3000"]');
  if (transcriptionCounter) {
    transcriptionCounter.setAttribute('data-target', getTranscriptionTarget());
  }



            const yearSpan = document.getElementById('current-year');
            if (yearSpan) {
                yearSpan.textContent = new Date().getFullYear();
            }

        });
    </script>

<script>
// Video autoplay/pause on visibility for all demo videos
function setupAutoVideo(id) {
  const video = document.getElementById(id);
  if (!video) return;

  // Ensure video is ready to play
  video.addEventListener('loadeddata', function() {
    console.log(`Video ${id} loaded and ready to play`);
  });

  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        // Check if the video's parent demo content is active (visible)
        const demoContent = video.closest('.demo-content');
        if (demoContent && demoContent.classList.contains('active')) {
          video.play().catch(e => {
            console.log(`Autoplay failed for ${id}:`, e);
          });
        }
      } else {
        video.pause();
        video.currentTime = 0;
      }
    });
  }, { threshold: 0.2 }); // 20% zichtbaar is genoeg
  observer.observe(video);
}

// Enhanced demo tab functionality to handle video playback
function setupDemoTabs() {
  const demoTabs = document.querySelectorAll('.demo-tab');
  const demoContents = document.querySelectorAll('.demo-content');

  demoTabs.forEach(tab => {
    tab.addEventListener('click', () => {
      const targetId = tab.getAttribute('data-target');
      const targetContent = document.getElementById(targetId);

      // Pause all videos first
      document.querySelectorAll('video[id^="video-"]').forEach(video => {
        video.pause();
        video.currentTime = 0;
      });

      // Remove active classes
      demoTabs.forEach(t => t.classList.remove('active'));
      demoContents.forEach(c => c.classList.remove('active'));

      // Add active classes
      tab.classList.add('active');
      if (targetContent) {
        targetContent.classList.add('active');

        // Start playing the video in the active tab if it's in view
        const video = targetContent.querySelector('video');
        if (video) {
          setTimeout(() => {
            video.play().catch(e => {
              console.log(`Autoplay failed for active tab video:`, e);
            });
          }, 100); // Small delay to ensure the content is visible
        }
      }
    });
  });
}

document.addEventListener('DOMContentLoaded', function() {
  setupAutoVideo('video-transcriberen');
  setupAutoVideo('video-vertalen');
  setupAutoVideo('video-dashboard');
  setupDemoTabs();
});
</script>

<script>
// Brand banner scroll hide behavior
document.addEventListener('DOMContentLoaded', function() {
    const brandBanner = document.querySelector('.brand-banner');
    if (brandBanner) {
        let lastScrollTop = 0;
        window.addEventListener('scroll', function() {
            let scrollTop = window.pageYOffset || document.documentElement.scrollTop;

            // If scrolled down more than 100px, hide the banner
            if (scrollTop > 100 && scrollTop > lastScrollTop) {
                brandBanner.classList.add('hidden');
            } else {
                // Show when scrolling up or at the top
                brandBanner.classList.remove('hidden');
            }
            lastScrollTop = scrollTop;
        });
    }
});
</script>
</body>
</html>
